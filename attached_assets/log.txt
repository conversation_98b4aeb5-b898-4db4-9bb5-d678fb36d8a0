INFO:backend.app:[GET /messages] Returning 1 messages to frontend
ERROR:backend.app:Error fetching case studies: (psycopg2.errors.UndefinedColumn) column case_studies.slug does not exist
LINE 1: ...ies_id, case_studies.title AS case_studies_title, case_studi...
                                                             ^

[SQL: SELECT case_studies.id AS case_studies_id, case_studies.title AS case_studies_title, case_studies.slug AS case_studies_slug, case_studies.client_id AS case_studies_client_id, case_studies.project_id AS case_studies_project_id, case_studies.summary AS case_studies_summary, case_studies.challenge AS case_studies_challenge, case_studies.solution AS case_studies_solution, case_studies.results AS case_studies_results, case_studies.testimonial AS case_studies_testimonial, case_studies.testimonial_author AS case_studies_testimonial_author, case_studies.testimonial_role AS case_studies_testimonial_role, case_studies.metrics AS case_studies_metrics, case_studies.hero_image AS case_studies_hero_image, case_studies.gallery_images AS case_studies_gallery_images, case_studies.industry AS case_studies_industry, case_studies.technologies AS case_studies_technologies, case_studies.services AS case_studies_services, case_studies.is_published AS case_studies_is_published, case_studies.is_featured AS case_studies_is_featured, case_studies.publication_date AS case_studies_publication_date, case_studies.meta_description AS case_studies_meta_description, case_studies.meta_keywords AS case_studies_meta_keywords, case_studies.created_at AS case_studies_created_at, case_studies.updated_at AS case_studies_updated_at 
FROM case_studies ORDER BY case_studies.created_at DESC]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:backend.app:API Error: (psycopg2.errors.UndefinedColumn) column case_studies.slug does not exist
LINE 1: ...ies_id, case_studies.title AS case_studies_title, case_studi...
                                                             ^

[SQL: SELECT case_studies.id AS case_studies_id, case_studies.title AS case_studies_title, case_studies.slug AS case_studies_slug, case_studies.client_id AS case_studies_client_id, case_studies.project_id AS case_studies_project_id, case_studies.summary AS case_studies_summary, case_studies.challenge AS case_studies_challenge, case_studies.solution AS case_studies_solution, case_studies.results AS case_studies_results, case_studies.testimonial AS case_studies_testimonial, case_studies.testimonial_author AS case_studies_testimonial_author, case_studies.testimonial_role AS case_studies_testimonial_role, case_studies.metrics AS case_studies_metrics, case_studies.hero_image AS case_studies_hero_image, case_studies.gallery_images AS case_studies_gallery_images, case_studies.industry AS case_studies_industry, case_studies.technologies AS case_studies_technologies, case_studies.services AS case_studies_services, case_studies.is_published AS case_studies_is_published, case_studies.is_featured AS case_studies_is_featured, case_studies.publication_date AS case_studies_publication_date, case_studies.meta_description AS case_studies_meta_description, case_studies.meta_keywords AS case_studies_meta_keywords, case_studies.created_at AS case_studies_created_at, case_studies.updated_at AS case_studies_updated_at 
FROM case_studies ORDER BY case_studies.created_at DESC]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:backend.app:Error fetching technologies: (psycopg2.errors.UndefinedColumn) column case_studies.slug does not exist
LINE 1: ...ies_id, case_studies.title AS case_studies_title, case_studi...
                                                             ^

[SQL: SELECT case_studies.id AS case_studies_id, case_studies.title AS case_studies_title, case_studies.slug AS case_studies_slug, case_studies.client_id AS case_studies_client_id, case_studies.project_id AS case_studies_project_id, case_studies.summary AS case_studies_summary, case_studies.challenge AS case_studies_challenge, case_studies.solution AS case_studies_solution, case_studies.results AS case_studies_results, case_studies.testimonial AS case_studies_testimonial, case_studies.testimonial_author AS case_studies_testimonial_author, case_studies.testimonial_role AS case_studies_testimonial_role, case_studies.metrics AS case_studies_metrics, case_studies.hero_image AS case_studies_hero_image, case_studies.gallery_images AS case_studies_gallery_images, case_studies.industry AS case_studies_industry, case_studies.technologies AS case_studies_technologies, case_studies.services AS case_studies_services, case_studies.is_published AS case_studies_is_published, case_studies.is_featured AS case_studies_is_featured, case_studies.publication_date AS case_studies_publication_date, case_studies.meta_description AS case_studies_meta_description, case_studies.meta_keywords AS case_studies_meta_keywords, case_studies.created_at AS case_studies_created_at, case_studies.updated_at AS case_studies_updated_at 
FROM case_studies 
WHERE case_studies.technologies IS NOT NULL]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:backend.app:API Error: (psycopg2.errors.UndefinedColumn) column case_studies.slug does not exist
LINE 1: ...ies_id, case_studies.title AS case_studies_title, case_studi...
                                                             ^

[SQL: SELECT case_studies.id AS case_studies_id, case_studies.title AS case_studies_title, case_studies.slug AS case_studies_slug, case_studies.client_id AS case_studies_client_id, case_studies.project_id AS case_studies_project_id, case_studies.summary AS case_studies_summary, case_studies.challenge AS case_studies_challenge, case_studies.solution AS case_studies_solution, case_studies.results AS case_studies_results, case_studies.testimonial AS case_studies_testimonial, case_studies.testimonial_author AS case_studies_testimonial_author, case_studies.testimonial_role AS case_studies_testimonial_role, case_studies.metrics AS case_studies_metrics, case_studies.hero_image AS case_studies_hero_image, case_studies.gallery_images AS case_studies_gallery_images, case_studies.industry AS case_studies_industry, case_studies.technologies AS case_studies_technologies, case_studies.services AS case_studies_services, case_studies.is_published AS case_studies_is_published, case_studies.is_featured AS case_studies_is_featured, case_studies.publication_date AS case_studies_publication_date, case_studies.meta_description AS case_studies_meta_description, case_studies.meta_keywords AS case_studies_meta_keywords, case_studies.created_at AS case_studies_created_at, case_studies.updated_at AS case_studies_updated_at 
FROM case_studies 
WHERE case_studies.technologies IS NOT NULL]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:backend.app:Error fetching sectors: type object 'CaseStudy' has no attribute 'primary_sector'
ERROR:backend.app:API Error: type object 'CaseStudy' has no attribute 'primary_sector'
ERROR:backend.app:Error fetching available projects: (psycopg2.errors.UndefinedColumn) column case_studies.slug does not exist
LINE 1: ...ies_id, case_studies.title AS case_studies_title, case_studi...
                                                             ^

[SQL: SELECT case_studies.id AS case_studies_id, case_studies.title AS case_studies_title, case_studies.slug AS case_studies_slug, case_studies.client_id AS case_studies_client_id, case_studies.project_id AS case_studies_project_id, case_studies.summary AS case_studies_summary, case_studies.challenge AS case_studies_challenge, case_studies.solution AS case_studies_solution, case_studies.results AS case_studies_results, case_studies.testimonial AS case_studies_testimonial, case_studies.testimonial_author AS case_studies_testimonial_author, case_studies.testimonial_role AS case_studies_testimonial_role, case_studies.metrics AS case_studies_metrics, case_studies.hero_image AS case_studies_hero_image, case_studies.gallery_images AS case_studies_gallery_images, case_studies.industry AS case_studies_industry, case_studies.technologies AS case_studies_technologies, case_studies.services AS case_studies_services, case_studies.is_published AS case_studies_is_published, case_studies.is_featured AS case_studies_is_featured, case_studies.publication_date AS case_studies_publication_date, case_studies.meta_description AS case_studies_meta_description, case_studies.meta_keywords AS case_studies_meta_keywords, case_studies.created_at AS case_studies_created_at, case_studies.updated_at AS case_studies_updated_at 
FROM case_studies 
WHERE %(param_1)s = case_studies.project_id]
[parameters: {'param_1': 16}]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:backend.app:API Error: (psycopg2.errors.UndefinedColumn) column case_studies.slug does not exist
LINE 1: ...ies_id, case_studies.title AS case_studies_title, case_studi...
                                                             ^

[SQL: SELECT case_studies.id AS case_studies_id, case_studies.title AS case_studies_title, case_studies.slug AS case_studies_slug, case_studies.client_id AS case_studies_client_id, case_studies.project_id AS case_studies_project_id, case_studies.summary AS case_studies_summary, case_studies.challenge AS case_studies_challenge, case_studies.solution AS case_studies_solution, case_studies.results AS case_studies_results, case_studies.testimonial AS case_studies_testimonial, case_studies.testimonial_author AS case_studies_testimonial_author, case_studies.testimonial_role AS case_studies_testimonial_role, case_studies.metrics AS case_studies_metrics, case_studies.hero_image AS case_studies_hero_image, case_studies.gallery_images AS case_studies_gallery_images, case_studies.industry AS case_studies_industry, case_studies.technologies AS case_studies_technologies, case_studies.services AS case_studies_services, case_studies.is_published AS case_studies_is_published, case_studies.is_featured AS case_studies_is_featured, case_studies.publication_date AS case_studies_publication_date, case_studies.meta_description AS case_studies_meta_description, case_studies.meta_keywords AS case_studies_meta_keywords, case_studies.created_at AS case_studies_created_at, case_studies.updated_at AS case_studies_updated_at 
FROM case_studies 
WHERE %(param_1)s = case_studies.project_id]
[parameters: {'param_1': 16}]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:backend.app:API Error: Entity namespace for "core_competencies" has no property "is_active"
INFO:backend.app:[GET /messages] Returning 1 messages to frontend
ERROR:backend.app:API Error: type object 'MarketProspect' has no attribute 'fit_score'
INFO:backend.app:[GET /messages] Returning 1 messages to frontend
ERROR:backend.app:API Error: (psycopg2.errors.UndefinedColumn) column technical_offers.client_id does not exist
LINE 1: ...technical_offers.title AS technical_offers_title, technical_...
                                                             ^

[SQL: SELECT technical_offers.id AS technical_offers_id, technical_offers.title AS technical_offers_title, technical_offers.client_id AS technical_offers_client_id, technical_offers.description AS technical_offers_description, technical_offers.scope_of_work AS technical_offers_scope_of_work, technical_offers.deliverables AS technical_offers_deliverables, technical_offers.timeline AS technical_offers_timeline, technical_offers.estimated_value AS technical_offers_estimated_value, technical_offers.estimated_hours AS technical_offers_estimated_hours, technical_offers.hourly_rate AS technical_offers_hourly_rate, technical_offers.status AS technical_offers_status, technical_offers.sent_date AS technical_offers_sent_date, technical_offers.response_deadline AS technical_offers_response_deadline, technical_offers.created_by AS technical_offers_created_by, technical_offers.created_at AS technical_offers_created_at, technical_offers.updated_at AS technical_offers_updated_at 
FROM technical_offers ORDER BY technical_offers.created_at DESC]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:backend.app:API Error: Entity namespace for "core_competencies" has no property "is_active"
INFO:backend.app:[GET /messages] Returning 1 messages to frontend
ERROR:backend.app:API Error: Entity namespace for "bi_reports" has no property "status"

INFO:backend.app:✅ [Certifications API] Final response data: {'metrics': {'total_certifications': 2, 'active_certifications': 2, 'expiring_soon': 1, 'total_annual_cost': 5800.0, 'average_health_score': 75.0, 'compliance_score': 75.0}, 'certifications': [{'id': 1, 'standard_code': 'ISO_9001_2015', 'standard_name': 'ISO 9001:2015 - Sistema di Gestione per la Qualità', 'certificate_number': None, 'certifying_body': 'Da definire', 'issue_date': '2025-06-24', 'expiry_date': '2028-06-23', 'days_to_expiry': 1091, 'is_expiring_soon': False, 'status': 'active', 'health_score': 85, 'readiness_score': 0.0, 'responsible_person': {'id': 2, 'name': 'Daniele Sabetta'}, 'annual_cost': 3000.0, 'project': None}, {'id': 3, 'standard_code': 'ISO_14001_2015', 'standard_name': 'ISO 14001:2015 - Sistema di Gestione Ambientale', 'certificate_number': 'ISO14001-2022-003', 'certifying_body': 'TÜV SÜD', 'issue_date': '2022-09-29', 'expiry_date': '2025-08-24', 'days_to_expiry': 57, 'is_expiring_soon': True, 'status': 'active', 'health_score': 65, 'readiness_score': 0.0, 'responsible_person': {'id': 2, 'name': 'Daniele Sabetta'}, 'annual_cost': 2800.0, 'project': None}], 'upcoming_actions': [{'type': 'renewal_due', 'title': 'ISO 14001:2015 - Sistema di Gestione Ambientale - Rinnovo necessario', 'description': 'Certificazione scade tra 57 giorni', 'due_date': '2025-08-24', 'priority': 'high', 'certification_id': 3}], 'compliance_risks': [{'certification_name': 'ISO 14001:2015 - Sistema di Gestione Ambientale', 'certification_id': 3, 'risk_level': 'high', 'risk_factors': ['Scade tra 57 giorni', 'Health score basso: 65%'], 'days_to_expiry': 57, 'health_score': 65}]}

ERROR:backend.app:Error calculating overview metrics: Neither 'InstrumentedAttribute' object nor 'Comparator' object associated with AIInteraction.query has an attribute 'filter'
WARNING:services.data_aggregation:Error querying contracts: 'Contract' object has no attribute 'value'

INFO:backend.app:[GET /messages] Returning 1 messages to frontend
ERROR:backend.app:API Error: (psycopg2.errors.UndefinedColumn) column project_kpi_templates.name does not exist
LINE 1: ...ect_kpi_templates.id AS project_kpi_templates_id, project_kp...
                                                             ^

[SQL: SELECT project_kpi_templates.id AS project_kpi_templates_id, project_kpi_templates.name AS project_kpi_templates_name, project_kpi_templates.description AS project_kpi_templates_description, project_kpi_templates.unit AS project_kpi_templates_unit, project_kpi_templates.category AS project_kpi_templates_category, project_kpi_templates.calculation_method AS project_kpi_templates_calculation_method, project_kpi_templates.is_active AS project_kpi_templates_is_active, project_kpi_templates.calculation_formula AS project_kpi_templates_calculation_formula, project_kpi_templates.data_source AS project_kpi_templates_data_source, project_kpi_templates.created_at AS project_kpi_templates_created_at, project_kpi_templates.updated_at AS project_kpi_templates_updated_at 
FROM project_kpi_templates]
(Background on this error at: https://sqlalche.me/e/20/f405)