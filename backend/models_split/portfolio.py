# Portfolio and Business Intelligence Models
from .base import db, datetime, date, json

class CaseStudy(db.Model):
    """Case Studies per Business Intelligence e Marketing"""
    __tablename__ = 'case_studies'

    id = db.Column(db.Integer, primary_key=True)

    # Dati base
    title = db.Column(db.String(200), nullable=False)
    overview = db.Column(db.Text, nullable=False)
    content = db.Column(db.Text)  # Contenuto completo generato

    # Categorizzazione
    case_type = db.Column(db.String(50), nullable=False)  # 'use-case', 'success-story', 'case-study'
    primary_sector = db.Column(db.String(100))  # Settore principale
    secondary_sectors = db.Column(db.JSON)  # Lista settori estendibili

    # Collegamenti
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    client_id = db.Column(db.<PERSON>, db.<PERSON><PERSON>ey('clients.id'))

    # Dati tecnici e business
    technologies = db.Column(db.JSON)  # Lista tecnologie utilizzate
    business_kpis = db.Column(db.JSON)  # KPI e metriche di business
    implementation_duration = db.Column(db.Integer)  # Durata in giorni
    team_size = db.Column(db.Integer)  # Dimensione del team

    # Workflow e approvazione
    status = db.Column(db.String(50), default='draft')  # draft, approved, published
    generated_by_ai = db.Column(db.Boolean, default=False)
    ai_prompt_used = db.Column(db.Text)  # Prompt utilizzato per generazione AI
    target_audience = db.Column(db.String(100), default='internal')  # internal, external, both

    # Audit trail
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    approved_at = db.Column(db.DateTime)

    # Relationships
    project = db.relationship('Project', backref='case_studies')
    client = db.relationship('Client', backref='case_studies')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_case_studies')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_case_studies')

    def __repr__(self):
        return f'<CaseStudy {self.title} ({self.case_type})>'

    @property
    def display_status(self):
        """Nome leggibile dello stato"""
        statuses = {
            'draft': 'Bozza',
            'approved': 'Approvato',
            'published': 'Pubblicato'
        }
        return statuses.get(self.status, self.status)

    def to_dict(self):
        """Serializzazione per API"""
        return {
            'id': self.id,
            'title': self.title,
            'overview': self.overview,
            'content': self.content,
            'case_type': self.case_type,
            'primary_sector': self.primary_sector,
            'secondary_sectors': self.secondary_sectors or [],
            'project_id': self.project_id,
            'client_id': self.client_id,
            'technologies': self.technologies or [],
            'business_kpis': self.business_kpis or {},
            'implementation_duration': self.implementation_duration,
            'team_size': self.team_size,
            'status': self.status,
            'generated_by_ai': self.generated_by_ai,
            'target_audience': self.target_audience,
            'created_by': self.created_by,
            'approved_by': self.approved_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'display_status': self.display_status,
            'project': {
                'id': self.project.id,
                'name': self.project.name
            } if self.project else None,
            'client': {
                'id': self.client.id,
                'name': self.client.name
            } if self.client else None,
            'creator': {
                'id': self.creator.id,
                'full_name': self.creator.full_name
            } if self.creator else None
        }


class TechnicalOffer(db.Model):
    """Offerte tecniche generate da AI per Business Intelligence"""
    __tablename__ = 'technical_offers'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    core_competency_id = db.Column(db.Integer, db.ForeignKey('core_competencies.id'))
    target_sector = db.Column(db.String(100))
    technology_stack = db.Column(db.JSON)
    team_composition = db.Column(db.JSON)
    estimated_duration_days = db.Column(db.Integer)
    estimated_cost_min = db.Column(db.Float)
    estimated_cost_max = db.Column(db.Float)
    deliverables = db.Column(db.JSON)
    success_metrics = db.Column(db.JSON)
    risk_factors = db.Column(db.JSON)
    generated_by_ai = db.Column(db.Boolean, default=False)
    ai_prompt_used = db.Column(db.Text)
    status = db.Column(db.String(20), default='draft')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    core_competency = db.relationship('CoreCompetency', backref='technical_offers')
    creator = db.relationship('User', backref='created_technical_offers')

    def __repr__(self):
        return f'<TechnicalOffer {self.title}>'

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'core_competency_id': self.core_competency_id,
            'target_sector': self.target_sector,
            'technology_stack': self.technology_stack or [],
            'team_composition': self.team_composition or {},
            'estimated_duration_days': self.estimated_duration_days,
            'estimated_cost_min': self.estimated_cost_min,
            'estimated_cost_max': self.estimated_cost_max,
            'deliverables': self.deliverables or [],
            'success_metrics': self.success_metrics or [],
            'risk_factors': self.risk_factors or [],
            'generated_by_ai': self.generated_by_ai,
            'ai_prompt_used': self.ai_prompt_used,
            'status': self.status,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'core_competency': {
                'id': self.core_competency.id,
                'name': self.core_competency.name
            } if self.core_competency else None
        }


class MarketProspect(db.Model):
    """Market Prospects per Business Intelligence e Lead Generation"""
    __tablename__ = 'market_prospects'

    id = db.Column(db.Integer, primary_key=True)
    company_name = db.Column(db.String(200), nullable=False)
    sector = db.Column(db.String(100))
    size_category = db.Column(db.String(50))
    location = db.Column(db.String(100))
    website = db.Column(db.String(200))
    contact_info = db.Column(db.JSON)
    technology_needs = db.Column(db.JSON)
    matching_competencies = db.Column(db.JSON)
    fit_score = db.Column(db.Float)
    estimated_budget_min = db.Column(db.Float)
    estimated_budget_max = db.Column(db.Float)
    lead_status = db.Column(db.String(20), default='new')
    source = db.Column(db.String(50))
    source_data = db.Column(db.JSON)
    notes = db.Column(db.Text)
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    assignee = db.relationship('User', backref='assigned_prospects')

    def __repr__(self):
        return f'<MarketProspect {self.company_name}>'

    @property
    def budget_range(self):
        """Formatta il range di budget stimato"""
        if self.estimated_budget_min and self.estimated_budget_max:
            return f"€{self.estimated_budget_min:,.0f} - €{self.estimated_budget_max:,.0f}"
        elif self.estimated_budget_min:
            return f"Da €{self.estimated_budget_min:,.0f}"
        elif self.estimated_budget_max:
            return f"Fino a €{self.estimated_budget_max:,.0f}"
        return "Da definire"

    def to_dict(self):
        return {
            'id': self.id,
            'company_name': self.company_name,
            'sector': self.sector,
            'size_category': self.size_category,
            'location': self.location,
            'website': self.website,
            'contact_info': self.contact_info or {},
            'technology_needs': self.technology_needs or [],
            'matching_competencies': self.matching_competencies or [],
            'fit_score': self.fit_score,
            'estimated_budget_min': self.estimated_budget_min,
            'estimated_budget_max': self.estimated_budget_max,
            'lead_status': self.lead_status,
            'source': self.source,
            'source_data': self.source_data or {},
            'notes': self.notes,
            'assigned_to': self.assigned_to,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'assignee': {
                'id': self.assignee.id,
                'full_name': self.assignee.full_name
            } if self.assignee else None
        }


class BIReport(db.Model):
    """Business Intelligence Reports"""
    __tablename__ = 'bi_reports'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    report_type = db.Column(db.String(50))
    data_sources = db.Column(db.JSON)
    filters = db.Column(db.JSON)
    chart_config = db.Column(db.JSON)
    schedule_config = db.Column(db.JSON)
    export_formats = db.Column(db.JSON)
    is_scheduled = db.Column(db.Boolean, default=False)
    last_generated = db.Column(db.DateTime)
    next_generation = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='active')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    creator = db.relationship('User', backref='created_bi_reports')

    def __repr__(self):
        return f'<BIReport {self.name}>'

    @property
    def is_overdue(self):
        """Controlla se il report schedulato è in ritardo"""
        if not self.is_scheduled or not self.next_generation:
            return False
        return datetime.utcnow() > self.next_generation

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'report_type': self.report_type,
            'data_sources': self.data_sources or [],
            'filters': self.filters or {},
            'chart_config': self.chart_config or {},
            'schedule_config': self.schedule_config or {},
            'export_formats': self.export_formats or [],
            'is_scheduled': self.is_scheduled,
            'last_generated': self.last_generated.isoformat() if self.last_generated else None,
            'next_generation': self.next_generation.isoformat() if self.next_generation else None,
            'status': self.status,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'creator': {
                'id': self.creator.id,
                'full_name': self.creator.full_name
            } if self.creator else None
        }