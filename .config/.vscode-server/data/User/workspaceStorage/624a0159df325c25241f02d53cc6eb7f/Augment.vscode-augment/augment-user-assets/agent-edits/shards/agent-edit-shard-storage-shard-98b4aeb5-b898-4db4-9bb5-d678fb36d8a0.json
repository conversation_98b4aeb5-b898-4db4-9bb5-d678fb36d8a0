{"id": "shard-98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "checkpoints": {"98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/backend/models_split/portfolio.py": [{"sourceToolCallRequestId": "6b4ee358-e1fd-42c9-bb91-35fa6aec80bb", "timestamp": 0, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "26273b88-1830-441e-bf69-752bc45afac9", "timestamp": 1751128234169, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "9c9380a1-5a37-4402-b3d2-7f674d22a56c", "timestamp": 1751128234578, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "d82c293d-88a8-49aa-8c7d-dd58ad1da388", "timestamp": 1751128234578, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "c88d0a36-6501-4822-bbe1-af587d9e211e", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "ed1af1f3-987e-433c-b90c-04c3c8893311", "timestamp": 1751128759202, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "5d4e9090-28c5-4834-b8ce-f26cf4ba3bb0", "timestamp": 1751128759656, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "19943301-9154-4eda-913d-038961f0291d", "timestamp": 1751128759656, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "c68da502-9a84-447b-a83c-ffec7c1a7b37", "timestamp": 1751128785119, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "c68d6916-16f4-4ae1-99aa-a9f9a2f2ea12", "timestamp": 1751128785282, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "e868914f-97aa-4cdc-a1da-7cba66b677f2", "timestamp": 1751128785282, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "b41411ee-6432-41c2-9a4c-19b5c105718b", "timestamp": 1751128808646, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "0c79df6e-59d0-4070-ba9d-76d4862f6f02", "timestamp": 1751128808790, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "3ae0ae0a-aeb6-4838-8952-38772b951001", "timestamp": 1751128808790, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "e7ac2046-8291-4860-9e27-b06b4a1e0858", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "5ed0cd3f-c285-4ca0-a462-71c1a3982ddc", "timestamp": 1751129104180, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "433a193a-b320-4e44-ad5c-e62d6e68d4c8", "timestamp": 1751129104605, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "d70c7ccd-d30e-477d-825c-decca4d8a6bf", "timestamp": 1751129104605, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "85c6a6e9-a54a-4d79-88e5-489ae890f186", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "8c5e8c16-2d34-4af4-87e6-d7fa5829561c", "timestamp": 1751129147850, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "3d4deb7d-9cdc-4b4a-bd69-a7a4ac610e3d", "timestamp": 1751129148006, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}, {"sourceToolCallRequestId": "61550eef-5c53-40be-a986-067cc0d836a7", "timestamp": 1751129148006, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "be368fc8-a25a-411c-9adb-2c1b590dc589", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/portfolio.py"}}}], "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/backend/models_split/projects.py": [{"sourceToolCallRequestId": "0e3164a6-8b6e-44a2-b30c-94ec1ee7b56d", "timestamp": 0, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "1851b4b4-270c-4971-8875-01ebb8eaa0a7", "timestamp": 1751128313983, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "c3506e44-ff19-4dde-9bbe-71bb25543e55", "timestamp": 1751128314418, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "c8bbf4be-e5ec-4127-b0f7-4ce4c1be2a49", "timestamp": 1751128314418, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "90574c18-7fb0-4d56-ae68-26b3a20d04f0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}], "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/crm/contracts/ContractView.vue": [{"sourceToolCallRequestId": "5c1f39bd-a667-4a7e-98cf-73005da6e4d7", "timestamp": 0, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractView.vue"}}}, {"sourceToolCallRequestId": "2895a31b-1411-4026-abce-1b5a9152e06d", "timestamp": 1751128351285, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractView.vue"}}}, {"sourceToolCallRequestId": "5aedf049-27c8-477f-8687-74e5a02f90fd", "timestamp": 1751128351709, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractView.vue"}}}, {"sourceToolCallRequestId": "01697c78-8d71-4384-b80c-dad325519e9f", "timestamp": 1751128351709, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "90574c18-7fb0-4d56-ae68-26b3a20d04f0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractView.vue"}}}], "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/crm/contracts/ContractsList.vue": [{"sourceToolCallRequestId": "3bbf5a54-78c2-42c9-b31a-ef7226d876c6", "timestamp": 0, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractsList.vue"}}}, {"sourceToolCallRequestId": "73c06a4c-79f9-4b05-a8c5-f89777241ed8", "timestamp": 1751128368680, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractsList.vue"}}}, {"sourceToolCallRequestId": "6578da47-2a5b-4904-97c3-96d636261daa", "timestamp": 1751128369072, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractsList.vue"}}}, {"sourceToolCallRequestId": "d265b63c-e87e-4c44-81c0-a0e9169d7c39", "timestamp": 1751128369073, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "90574c18-7fb0-4d56-ae68-26b3a20d04f0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractsList.vue"}}}], "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/crm/CRMDashboard.vue": [{"sourceToolCallRequestId": "4bf6fd27-01b9-473e-b107-e170124ba0fe", "timestamp": 0, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/CRMDashboard.vue"}}}, {"sourceToolCallRequestId": "118dff4c-2112-405a-bce6-19f239c7fe5f", "timestamp": 1751128388014, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/CRMDashboard.vue"}}}, {"sourceToolCallRequestId": "553cba94-1bcf-4e43-8343-56daf695c26a", "timestamp": 1751128388467, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/CRMDashboard.vue"}}}, {"sourceToolCallRequestId": "2eb3d002-39e9-41d3-af15-e1a3b4b8276a", "timestamp": 1751128388467, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "90574c18-7fb0-4d56-ae68-26b3a20d04f0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/CRMDashboard.vue"}}}], "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/crm/contracts/ContractForm.vue": [{"sourceToolCallRequestId": "a5f2c0dd-18f5-4c6a-8165-6cb2c5189590", "timestamp": 0, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractForm.vue"}}}, {"sourceToolCallRequestId": "3ca5a3d7-4cc1-49e5-b2a5-f71caa880fba", "timestamp": 1751128404095, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractForm.vue"}}}, {"sourceToolCallRequestId": "b69778d2-1641-440e-a4b0-a38956be180c", "timestamp": 1751128404713, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractForm.vue"}}}, {"sourceToolCallRequestId": "c0bf9405-5b75-4fda-a9d5-ce50907cfbb7", "timestamp": 1751128404713, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "90574c18-7fb0-4d56-ae68-26b3a20d04f0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/contracts/ContractForm.vue"}}}], "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/admin/Admin.vue": [{"sourceToolCallRequestId": "75ad3284-328f-42d1-907c-1ab7f9b93a02", "timestamp": 0, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/admin/Admin.vue"}}}, {"sourceToolCallRequestId": "be2fc11d-0bcd-4414-a0c7-a4b2c4cf21aa", "timestamp": 1751128418959, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/admin/Admin.vue"}}}, {"sourceToolCallRequestId": "19f3e8ba-81a2-4cce-b61f-5d33cebc7d03", "timestamp": 1751128419484, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/admin/Admin.vue"}}}, {"sourceToolCallRequestId": "20e0b5fa-0471-4895-921a-0a1399b72732", "timestamp": 1751128419484, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "90574c18-7fb0-4d56-ae68-26b3a20d04f0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/admin/Admin.vue"}}}], "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/crm/proposals/ProposalView.vue": [{"sourceToolCallRequestId": "488dc02e-a0c4-4146-b12e-a393ecf26f4f", "timestamp": 0, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/proposals/ProposalView.vue"}}}, {"sourceToolCallRequestId": "3e8cbe8f-d9b7-4449-93cc-3395fe8e09d8", "timestamp": 1751128435404, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/proposals/ProposalView.vue"}}}, {"sourceToolCallRequestId": "dbeba091-746d-4a5a-a465-392f3f1220a5", "timestamp": 1751128435854, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/proposals/ProposalView.vue"}}}, {"sourceToolCallRequestId": "6502bea4-b2f2-438e-a7fe-69d0da894956", "timestamp": 1751128435854, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "90574c18-7fb0-4d56-ae68-26b3a20d04f0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/crm/proposals/ProposalView.vue"}}}], "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/backend/models_split/invoicing.py": [{"sourceToolCallRequestId": "c7e21f25-04bb-4b47-a05a-a95c507dc2bd", "timestamp": 0, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/invoicing.py"}}}, {"sourceToolCallRequestId": "00c81ae0-0ad3-4942-b17c-df248c6e746e", "timestamp": 1751128592420, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/invoicing.py"}}}, {"sourceToolCallRequestId": "f69c64ce-42df-4340-9cbb-428268c7750c", "timestamp": 1751128592879, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/invoicing.py"}}}, {"sourceToolCallRequestId": "ecbd06d6-bd44-4358-ad31-f35b79923b5a", "timestamp": 1751128592879, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "53ea1062-dc08-432c-b17e-42aceb584fb8", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/invoicing.py"}}}], "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/backend/tests/unit/test_integrationsettings_model.py": [{"sourceToolCallRequestId": "0957ffdb-5f69-4183-9bf1-3e33825d4343", "timestamp": 0, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_integrationsettings_model.py"}}}, {"sourceToolCallRequestId": "a86367d0-b775-42c6-bb99-bacf34aa5b6d", "timestamp": 1751128609473, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_integrationsettings_model.py"}}}, {"sourceToolCallRequestId": "42516c3d-3b1f-4b38-b46a-8b35eb363c0b", "timestamp": 1751128609983, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_integrationsettings_model.py"}}}, {"sourceToolCallRequestId": "29adb4b9-62e0-4b63-bd02-003b7a79dd31", "timestamp": 1751128609983, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "53ea1062-dc08-432c-b17e-42aceb584fb8", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_integrationsettings_model.py"}}}], "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/backend/models_split/performance.py": [{"sourceToolCallRequestId": "8bab5400-bbd1-41eb-b9f7-667267dd2257", "timestamp": 0, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/performance.py"}}}, {"sourceToolCallRequestId": "5af7e274-42cc-41ca-92fa-cb1143b61fd1", "timestamp": 1751128720836, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/performance.py"}}}, {"sourceToolCallRequestId": "99fa507c-2eef-483f-9e16-f14fdbc8b165", "timestamp": 1751128721421, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/performance.py"}}}, {"sourceToolCallRequestId": "a8dbd883-e471-42a0-89c0-da8ac3f1de5e", "timestamp": 1751128721421, "conversationId": "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0", "editSource": 1, "lastIncludedInRequestId": "e7ac2046-8291-4860-9e27-b06b4a1e0858", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/performance.py"}}}]}, "metadata": {"checkpointDocumentIds": ["98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/backend/models_split/portfolio.py", "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/backend/models_split/projects.py", "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/crm/contracts/ContractView.vue", "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/crm/contracts/ContractsList.vue", "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/crm/CRMDashboard.vue", "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/crm/contracts/ContractForm.vue", "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/admin/Admin.vue", "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/frontend/src/views/crm/proposals/ProposalView.vue", "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/backend/models_split/invoicing.py", "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/backend/tests/unit/test_integrationsettings_model.py", "98b4aeb5-b898-4db4-9bb5-d678fb36d8a0:/home/<USER>/workspace/backend/models_split/performance.py"], "size": 1819405, "checkpointCount": 59, "lastModified": 1751129462971}}