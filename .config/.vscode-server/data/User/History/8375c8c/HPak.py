"""
API per l'analisi dell'allocazione delle risorse del personale.
Fornisce viste aggregate temporali con confronto pianificato vs effettivo.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import func, and_, or_
from datetime import datetime, timedelta, date
import calendar
from models import User, Project, ProjectResource, Task, TimesheetEntry
from sqlalchemy.orm import aliased
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission, PERMISSION_VIEW_ALL_PROJECTS
from extensions import db

# Crea il blueprint per l'analisi allocazione personale
api_personnel_allocation = Blueprint('api_personnel_allocation', __name__, url_prefix='/allocation')

@api_personnel_allocation.route('/allocation-filters', methods=['GET'])
@login_required
def get_allocation_filters():
    """
    Ottieni i filtri disponibili per l'analisi allocazione.
    ---
    tags:
      - personnel-allocation
    responses:
      200:
        description: Filtri disponibili
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                data:
                  type: object
                  properties:
                    departments:
                      type: array
                      description: Lista dipartimenti
                    projects:
                      type: array
                      description: Lista progetti con allocazioni
    """
    try:
        # Ottieni dipartimenti con utenti attivi
        departments_query = db.session.execute(db.text("""
            SELECT d.id, d.name, COUNT(u.id) as user_count
            FROM departments d
            JOIN "users" u ON d.id = u.department_id
            WHERE u.is_active = true
            GROUP BY d.id, d.name
            ORDER BY d.name
        """))

        departments = []
        for dept in departments_query.all():
            departments.append({
                'id': dept[0],
                'name': dept[1],
                'user_count': dept[2]
            })

        # Ottieni progetti con allocazioni attive
        projects_query = db.session.query(
            Project.id,
            Project.name,
            func.count(ProjectResource.user_id).label('allocated_users')
        ).join(
            ProjectResource, Project.id == ProjectResource.project_id
        ).join(
            User, ProjectResource.user_id == User.id
        ).filter(
            User.is_active == True
        ).group_by(
            Project.id, Project.name
        ).order_by(
            Project.name
        )

        projects = []
        for proj in projects_query.all():
            projects.append({
                'id': proj.id,
                'name': proj.name,
                'allocated_users': proj.allocated_users
            })

        return api_response(data={
            'departments': departments,
            'projects': projects
        })

    except Exception as e:
        current_app.logger.error(f"Error in get_allocation_filters: {str(e)}")
        return handle_api_error(e)

@api_personnel_allocation.route('/test', methods=['GET'])
def test_endpoint():
    """Test endpoint per verificare che il blueprint funzioni"""
    return api_response(data={'message': 'Blueprint personnel allocation funziona!'})

@api_personnel_allocation.route('/allocation-analysis', methods=['GET'])
@login_required
def get_allocation_analysis():
    """
    Analizza l'allocazione delle risorse con confronto temporale.
    ---
    tags:
      - personnel-allocation
    parameters:
      - name: period
        in: query
        description: Periodo di analisi
        schema:
          type: string
          enum: [current-month, current-quarter, current-year, next-quarter]
          default: current-month
    responses:
      200:
        description: Analisi dell'allocazione
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    summary:
                      type: array
                      description: Riepilogo per persona
                    detailed:
                      type: array
                      description: Vista dettagliata per allocazione
                    period_info:
                      type: object
                      description: Informazioni sul periodo analizzato
    """
    try:
        # Verifica permessi
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            return api_response(
                message="Non hai i permessi per visualizzare l'analisi dell'allocazione",
                status_code=403
            )

        # Ottieni parametri
        period = request.args.get('period', 'current-month')
        department_id = request.args.get('department_id', type=int)
        project_id = request.args.get('project_id', type=int)
        only_allocated = request.args.get('only_allocated', 'false').lower() == 'true'

        # Calcola date del periodo
        period_info = calculate_period_dates(period)
        start_date = period_info['start_date']
        end_date = period_info['end_date']

        # Costruisci query per utenti con filtri
        users_query = User.query.filter(User.is_active == True)

        # Filtro per dipartimento
        if department_id:
            users_query = users_query.filter(User.department_id == department_id)

        # Se richiesto solo utenti allocati, filtra per quelli con ProjectResource
        if only_allocated or project_id:
            users_query = users_query.join(ProjectResource, User.id == ProjectResource.user_id)
            if project_id:
                users_query = users_query.filter(ProjectResource.project_id == project_id)
            users_query = users_query.distinct()

        users = users_query.all()

        summary_data = []
        detailed_data = []

        for user in users:
            # Calcola capacità lavorativa per il periodo
            capacity_hours = calculate_user_capacity(user, start_date, end_date)

            # Ottieni allocazioni per il periodo
            allocations = get_user_allocations(user.id, start_date, end_date)

            # Calcola ore effettive dai timesheet
            actual_hours = calculate_actual_hours(user.id, start_date, end_date)

            # Calcola ore allocate totali
            total_allocated_hours = sum(alloc['allocated_hours'] for alloc in allocations)

            # Calcola utilizzo
            utilization_percentage = (actual_hours / capacity_hours * 100) if capacity_hours > 0 else 0

            # Dati summary per persona
            user_summary = {
                'user_id': user.id,
                'user_name': f"{user.first_name} {user.last_name}",
                'role': user.role,
                'capacity_hours': capacity_hours,
                'allocated_hours': total_allocated_hours,
                'actual_hours': actual_hours,
                'utilization_percentage': utilization_percentage,
                'projects': allocations
            }
            summary_data.append(user_summary)

            # Dati detailed per ogni allocazione
            for allocation in allocations:
                project_actual_hours = calculate_project_actual_hours(
                    user.id, allocation['project_id'], start_date, end_date
                )

                variance = project_actual_hours - allocation['allocated_hours']

                detailed_allocation = {
                    'user_id': user.id,
                    'user_name': f"{user.first_name} {user.last_name}",
                    'project_id': allocation['project_id'],
                    'project_name': allocation['project_name'],
                    'project_period': f"{allocation['project_start']} - {allocation['project_end']}",
                    'role': allocation['role'],
                    'allocation_percentage': allocation['allocation_percentage'],
                    'planned_hours': allocation['allocated_hours'],
                    'actual_hours': project_actual_hours,
                    'variance': variance
                }
                detailed_data.append(detailed_allocation)

        return api_response(data={
            'summary': summary_data,
            'detailed': detailed_data,
            'period_info': period_info
        })

    except Exception as e:
        current_app.logger.error(f"Error in get_allocation_analysis: {str(e)}")
        return handle_api_error(e)

def calculate_period_dates(period):
    """Calcola le date di inizio e fine per il periodo specificato."""
    today = date.today()

    if period == 'current-month':
        start_date = today.replace(day=1)
        # Ultimo giorno del mese
        _, last_day = calendar.monthrange(today.year, today.month)
        end_date = today.replace(day=last_day)
        description = f"Mese corrente ({start_date.strftime('%B %Y')})"

    elif period == 'current-quarter':
        quarter = (today.month - 1) // 3 + 1
        start_date = date(today.year, (quarter - 1) * 3 + 1, 1)
        # Ultimo mese del trimestre
        end_month = quarter * 3
        _, last_day = calendar.monthrange(today.year, end_month)
        end_date = date(today.year, end_month, last_day)
        description = f"Q{quarter} {today.year}"

    elif period == 'current-year':
        start_date = date(today.year, 1, 1)
        end_date = date(today.year, 12, 31)
        description = f"Anno {today.year}"

    elif period == 'next-quarter':
        quarter = (today.month - 1) // 3 + 2
        if quarter > 4:
            quarter = 1
            year = today.year + 1
        else:
            year = today.year
        start_date = date(year, (quarter - 1) * 3 + 1, 1)
        # Ultimo mese del trimestre
        end_month = quarter * 3
        _, last_day = calendar.monthrange(year, end_month)
        end_date = date(year, end_month, last_day)
        description = f"Q{quarter} {year}"

    else:
        # Default a mese corrente
        start_date = today.replace(day=1)
        _, last_day = calendar.monthrange(today.year, today.month)
        end_date = today.replace(day=last_day)
        description = f"Mese corrente ({start_date.strftime('%B %Y')})"

    return {
        'start_date': start_date,
        'end_date': end_date,
        'description': description,
        'period': period
    }

def calculate_user_capacity(user, start_date, end_date):
    """Calcola la capacità lavorativa di un utente per il periodo."""
    # Calcola giorni lavorativi (assumendo 5 giorni/settimana, 8 ore/giorno)
    total_days = (end_date - start_date).days + 1

    # Stima approssimativa: 5/7 dei giorni sono lavorativi
    working_days = int(total_days * 5 / 7)

    # 8 ore per giorno lavorativo (questo potrebbe essere configurabile per utente)
    hours_per_day = 8

    return working_days * hours_per_day

def get_user_allocations(user_id, start_date, end_date):
    """Ottieni le allocazioni di un utente per il periodo."""
    allocations = db.session.query(
        ProjectResource.project_id,
        ProjectResource.allocation_percentage,
        ProjectResource.role,
        Project.name.label('project_name'),
        Project.start_date.label('project_start'),
        Project.end_date.label('project_end')
    ).join(
        Project, ProjectResource.project_id == Project.id
    ).filter(
        ProjectResource.user_id == user_id,
        or_(
            Project.end_date.is_(None),  # Progetti senza data fine
            Project.end_date >= start_date  # Progetti che finiscono dopo l'inizio del periodo
        ),
        or_(
            Project.start_date.is_(None),  # Progetti senza data inizio
            Project.start_date <= end_date  # Progetti che iniziano prima della fine del periodo
        )
    ).all()

    result = []
    for alloc in allocations:
        # Calcola ore allocate per il periodo
        project_start = alloc.project_start or start_date
        project_end = alloc.project_end or end_date

        # Intersezione del periodo progetto con il periodo di analisi
        period_start = max(start_date, project_start)
        period_end = min(end_date, project_end)

        if period_start <= period_end:
            # Calcola giorni lavorativi nel periodo
            period_days = (period_end - period_start).days + 1
            working_days = int(period_days * 5 / 7)

            # Calcola ore allocate
            allocated_hours = working_days * 8 * (alloc.allocation_percentage / 100)

            result.append({
                'project_id': alloc.project_id,
                'project_name': alloc.project_name,
                'project_start': project_start.strftime('%Y-%m-%d') if project_start else None,
                'project_end': project_end.strftime('%Y-%m-%d') if project_end else None,
                'allocation_percentage': alloc.allocation_percentage,
                'role': alloc.role,
                'allocated_hours': round(allocated_hours, 1)
            })

    return result

def calculate_actual_hours(user_id, start_date, end_date):
    """Calcola le ore effettive lavorate da un utente nel periodo."""
    total_hours = db.session.query(
        func.sum(TimesheetEntry.hours)
    ).filter(
        TimesheetEntry.user_id == user_id,
        TimesheetEntry.date >= start_date,
        TimesheetEntry.date <= end_date,
        TimesheetEntry.status == 'approved'  # Solo timesheet approvati
    ).scalar()

    return float(total_hours or 0)

def calculate_project_actual_hours(user_id, project_id, start_date, end_date):
    """Calcola le ore effettive lavorate da un utente su un progetto specifico."""
    total_hours = db.session.query(
        func.sum(TimesheetEntry.hours)
    ).filter(
        TimesheetEntry.user_id == user_id,
        TimesheetEntry.project_id == project_id,
        TimesheetEntry.date >= start_date,
        TimesheetEntry.date <= end_date,
        TimesheetEntry.status == 'approved'
    ).scalar()

    return float(total_hours or 0)
